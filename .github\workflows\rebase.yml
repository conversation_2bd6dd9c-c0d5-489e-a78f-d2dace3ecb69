on:
  issue_comment:
    types: [created]
name: Automatic Rebase
jobs:
  rebase:
    name: Rebase
    if: github.event.issue.pull_request != '' && contains(github.event.comment.body, '/rebase')
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@master
      with:
        fetch-depth: 0
    - name: Automatic Rebase
      uses: cirrus-actions/rebase@master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
