{"compileOnSave": true, "compilerOptions": {"target": "es2017", "module": "commonjs", "strict": true, "noImplicitAny": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "charset": "utf8", "allowJs": false, "pretty": true, "noEmitOnError": false, "noUnusedLocals": true, "noUnusedParameters": true, "allowUnreachableCode": false, "allowUnusedLabels": false, "strictPropertyInitialization": false, "noFallthroughCasesInSwitch": true, "skipLibCheck": true, "skipDefaultLibCheck": true, "inlineSourceMap": true, "importHelpers": true}, "exclude": ["app/public", "app/views", "node_modules*"]}