{"compilerOptions": {"outDir": "build/dist", "module": "esnext", "target": "esnext", "lib": ["esnext", "dom"], "sourceMap": true, "baseUrl": ".", "jsx": "react", "allowSyntheticDefaultImports": true, "moduleResolution": "node", "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "suppressImplicitAnyIndexErrors": true, "noUnusedLocals": true, "allowJs": true, "skipLibCheck": true, "experimentalDecorators": true, "strict": true, "paths": {"@/*": ["./src/*"], "@@/*": ["./src/.umi/*"]}}, "include": ["mock/**/*", "src/**/*", "config/**/*", ".umirc.ts"], "exclude": ["node_modules", "build", "dist", "scripts", "src/.umi/*", "webpack", "jest"]}