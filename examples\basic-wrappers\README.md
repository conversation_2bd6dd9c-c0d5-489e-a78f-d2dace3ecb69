# basic-wrappers

wrappers is HOC.you can run authorization check for a specific route.

See our website [wrappers](https://umijs.org/docs/routing#wrappers) for more information.

## How to use

Execute [`@umijs/create-umi-app`](https://github.com/umijs/umi/tree/3.x/packages/create-umi-app) with [npm](https://docs.npmjs.com/cli/init) or [Yarn](https://yarnpkg.com/lang/en/docs/cli/create/) to bootstrap the example:

```bash
npx @umijs/create-umi-app --example basic-wrappers basic-wrappers-app
# or
yarn create @umijs/umi-app --example basic-wrappers basic-wrappers-app
```
