# config-dynamicImport

Whether to enable on-demand loading, that is, whether to split the build product and download additional JS when necessary.

See our website [config/dynamicimport](https://umijs.org/config#dynamicimport) for more information.

## How to use

Execute [`@umijs/create-umi-app`](https://github.com/umijs/umi/tree/3.x/packages/create-umi-app) with [npm](https://docs.npmjs.com/cli/init) or [Yarn](https://yarnpkg.com/lang/en/docs/cli/create/) to bootstrap the example:

```bash
npx @umijs/create-umi-app --example config-dynamicImport config-dynamicImport-app
# or
yarn create @umijs/umi-app --example config-dynamicImport config-dynamicImport-app
```
