---
title: Umi<PERSON>S - Extensible enterprise-level front-end application framework.
hero:
  title: UmiJS
  desc: 🍙 Extensible enterprise-level front-end application framework.
  actions:
    - text: Get Started →
      link: /docs/getting-started
features:
  - icon: https://gw.alipayobjects.com/zos/basement_prod/a1c647aa-a410-4024-8414-c9837709cb43/k7787itw_w126_h114.png
    title: Extensible
    desc: <PERSON>i implements the complete lifecycle and makes it extensible, and <PERSON><PERSON>'s internal functions are all plugins. Umi also support plugins and presets.
  - icon: https://gw.alipayobjects.com/zos/basement_prod/b54b48c7-087a-4984-b150-bcecb40920de/k7787z07_w114_h120.png
    title: Out of the Box
    desc: <PERSON>i has built-in routing, building, deployment, testing, and so on. It only requires one dependency to get started. <PERSON><PERSON> also provides an integrated preset for React with rich functionaries.
  - icon: https://gw.alipayobjects.com/zos/basement_prod/464cb990-6db8-4611-89af-7766e208b365/k77899wk_w108_h132.png
    title: Enterprise
    desc: It has been verified by 3000+ projects in Ant Group and projects of Alibaba, Youku, Netease, 飞猪, KouBei and other companies.
  - icon: https://gw.alipayobjects.com/zos/basement_prod/201bea40-cf9d-4be2-a1d8-55bec136faf2/k7788a8s_w102_h120.png
    title: Self Development
    desc: Including micro frontend library, component packaging, documentation tools, request library, hooks library, data flow, etc.
  - icon: https://gw.alipayobjects.com/zos/basement_prod/67b771c5-4bdd-4384-80a4-978b85f91282/k7788ov2_w126_h126.png
    title: Perfect Routing
    desc: Supports both configuration routing and convention routing, while with functional completeness, such as dynamic routing, nested routing, permission routing, and so on.
  - icon: https://gw.alipayobjects.com/zos/basement_prod/d078a5a9-1cb3-4352-9f05-505c2e98bc95/k7788v4b_w102_h126.png
    title: Future Ready
    desc: Umi's community is also exploring new technologies. For example, modern mode, webpack @ 5, automated external, bundler less, etc.
footer: Open-source MIT Licensed | Copyright © 2017-present<br />Powered by [dumi](https://d.umijs.org/).
---

## Getting Started in 3 minutes

[![Edit umi](https://codesandbox.io/static/img/play-codesandbox.svg)](https://codesandbox.io/s/umi-2d4js?autoresize=1&fontsize=14&hidenavigation=1&module=%2Fsrc%2Fpages%2Findex.tsx&theme=dark)

Manually,

```bash
# Create directory
$ mkdir myapp && cd myapp

# Install dependency
$ yarn add umi

# Create page
$ npx umi g page index --typescript --less

# Start development
$ npx umi dev
```

Or [Getting Started with Boilerplate](/docs/getting-started).

## Contributors

This project exists thanks to all the people who contribute. [Join us!](/docs/contributing)

<a href="https://github.com/umijs/umi/graphs/contributors"><img src="https://opencollective.com/umi/contributors.svg?width=960&button=false" /></a>

## Feedback

| Github Issue | 微信群 |
| --- | --- |
| [umijs/umi/issues](https://github.com/umijs/umi/issues) | <img src="https://img.alicdn.com/imgextra/i1/O1CN01jmrjUx1yw5LcPFMx0_!!6000000006642-0-tps-430-430.jpg" width="60" /> 关注后回复「umi」 |


