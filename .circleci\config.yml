version: 2.1
jobs:
  build:
    docker:
      - image: circleci/node:12
    working_directory: ~/umi

    steps:
      - checkout
      - attach_workspace:
          at: ~/umi
      - restore_cache:
          key: node-modules-{{ checksum "yarn.lock" }}
      - run:
          name: Install Dependencies
          command: yarn --frozen-lockfile
      - save_cache:
          key: node-modules-{{ checksum "yarn.lock" }}
          paths:
            - ./node_modules
            - ~/.cache/yarn
      - run:
          name: Run Build
          command: yarn build
      - run:
          name: Run Type Check
          command: yarn run tsc --noEmit
      - run:
          name: Run Tests
          command: NODE_OPTIONS='--max-old-space-size=4096' yarn test:coverage --forceExit --detectOpenHandles --runInBand
          no_output_timeout: 300m
      - run:
          name: Generate coverage
          command: bash <(curl -s https://codecov.io/bash)
