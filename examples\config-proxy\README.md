# config-proxy

The development environment uses a proxy to request data

## How to use

Execute [`@umijs/create-umi-app`](https://github.com/umijs/umi/tree/3.x/packages/create-umi-app) with [npm](https://docs.npmjs.com/cli/init) or [Yarn](https://yarnpkg.com/lang/en/docs/cli/create/) to bootstrap the example:

```bash
npx @umijs/create-umi-app --example config-proxy config-proxy-app
# or
yarn create @umijs/umi-app --example config-proxy config-proxy-app
```
