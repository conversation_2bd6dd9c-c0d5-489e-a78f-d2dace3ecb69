---
name: 'Bug report（缺陷问题反馈）'
about: 'Report a bug to help us improve'
title: '[Bug] say something'
labels: ''
assignees: ''
---

<!--
⚠️ ⚠️ ⚠️ 注意：讨论和提问请到讨论区（https://github.com/umijs/umi/discussions），否则会被直接关掉。 ⚠️ ⚠️ ⚠️
-->
<!--
感谢您向我们反馈问题，为了高效的解决问题，我们期望你能提供以下信息：
-->

## What happens?

<!-- A clear and concise description of what the bug is. -->
<!-- 清晰的描述下遇到的问题。-->

## Mini Showcase Repository(REQUIRED)

> Please provide a repository using `yarn create @umijs/umi-app` then upload to your GitHub 请提供一个可复现的仓库，使用 `yarn create @umijs/umi-app` 创建，并上传到你的 GitHub 仓库

<!-- 为节约大家的时间，无复现步骤的 ISSUE 会被关闭，提供之后再 REOPEN -->
<!-- https://github.com/YOUR_REPOSITORY_URL -->

## How To Reproduce

**Steps to reproduce the behavior:** 1. 2.

**Expected behavior** 1. 2.

<!-- 请提供复现链接/步骤，错误日志以及相关配置 -->

## Context

- **Umi Version**:
- **Node Version**:
- **Platform**:
