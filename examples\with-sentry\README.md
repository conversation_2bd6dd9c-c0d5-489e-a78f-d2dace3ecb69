# with-sentry

umi with [<PERSON><PERSON>](https://github.com/getsentry/sentry) - Sentry is cross-platform application monitoring, with a focus on error reporting.

## How to use

Execute [`@umijs/create-umi-app`](https://github.com/umijs/umi/tree/3.x/packages/create-umi-app) with [npm](https://docs.npmjs.com/cli/init) or [Yarn](https://yarnpkg.com/lang/en/docs/cli/create/) to bootstrap the example:

```bash
npx @umijs/create-umi-app --example with-sentry with-sentry-app
# or
yarn create @umijs/umi-app --example with-sentry with-sentry-app
```
