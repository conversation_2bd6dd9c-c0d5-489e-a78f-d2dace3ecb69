# with-dva

umi with dva - React and redux based, lightweight and elm-style framework.

See our website [@umijs/plugin-dva](https://umijs.org/plugins/plugin-dva) for more information.

## How to use

Execute [`@umijs/create-umi-app`](https://github.com/umijs/umi/tree/3.x/packages/create-umi-app) with [npm](https://docs.npmjs.com/cli/init) or [Yarn](https://yarnpkg.com/lang/en/docs/cli/create/) to bootstrap the example:

```bash
npx @umijs/create-umi-app --example with-dva with-dva-app
# or
yarn create @umijs/umi-app --example with-dva with-dva-app
```
