# convention-mock

Use mock to help us develop

more config : [https://umijs.org/zh-CN/docs/mock#mock-%E6%95%B0%E6%8D%AE](https://umijs.org/zh-CN/docs/mock#mock-%E6%95%B0%E6%8D%AE)

## How to use

Execute [`@umijs/create-umi-app`](https://github.com/umijs/umi/tree/3.x/packages/create-umi-app) with [npm](https://docs.npmjs.com/cli/init) or [Yarn](https://yarnpkg.com/lang/en/docs/cli/create/) to bootstrap the example:

```bash
npx @umijs/create-umi-app --example convention-mock convention-mock-app
# or
yarn create @umijs/umi-app --example convention-mock convention-mock-app
```
