name: Node CI

env:
  NODE_OPTIONS: --max-old-space-size=6144

on:
  push:
    paths-ignore:
      - '.github/**/*.md'
      - 'examples/**'
      - 'docs/**'
      - '*.md'

jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        node_version: [12.x, 14.x]
        os: [ubuntu-latest, macos-latest, windows-latest]
      fail-fast: false
    steps:
      - uses: actions/checkout@v2
        with:
          fetch-depth: 0
      - name: Use Node.js ${{ matrix.node_version }}
        uses: actions/setup-node@v2
        with:
          node-version: ${{ matrix.node_version }}
          registry-url: 'https://registry.yarnpkg.com'
      - name: restore lerna
        id: cache
        uses: actions/cache@v2
        with:
          path: |
            node_modules
            */*/node_modules
          key: ${{ runner.os }}-${{ hashFiles('**/yarn.lock') }}
      - name: install
        if: steps.cache.outputs.cache-hit != 'true'
        run: yarn --ignore-engines --frozen-lockfile
      - run: yarn build
      - run: yarn run tsc --noEmit
      - run: yarn test --detectOpenHandles --maxWorkers=1 --forceExit
        env:
          CI: true
          HEADLESS: false
          PROGRESS: none
          NODE_ENV: test
