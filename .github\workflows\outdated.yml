name: Outdated Version Action

on:
  issue_comment:
    types: [created]

jobs:
  outdated-version:
    if: github.event.issue.pull_request != '' && contains(github.event.comment.body, '/version')
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: install
        run: yarn --ignore-engines
      - name: outdated version
        uses: ycjcl868/outdated-version-action@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
