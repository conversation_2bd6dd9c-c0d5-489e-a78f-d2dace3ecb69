
# How Umi Works

The thoughts and important concepts behind Um<PERSON>.

## Technology convergence

<img src="https://img.alicdn.com/tfs/TB1hE8ywrr1gK0jSZFDXXb9yVXa-1227-620.png" width="600">

This picture is for <PERSON><PERSON>, the internal frame, and it is also suitable for Um<PERSON>. He sorted out the technology stacks that everyone commonly uses, and converged them together, so that everyone can use Umi to complete 80% of their daily work.

## Plugins and plugin sets

<img src="https://img.alicdn.com/tfs/TB1mrhuwqL7gK0jSZFBXXXZZpXa-956-728.png" width="400">

Umi supports plug-ins and plug-in sets. The relationship between them should be well understood through this picture. Through plug-in sets, we rely on plug-in convergence and support different business types.

## Configurational routing and contractual routing

Umi's routing supports both configuration and convention. The configuration style is a bow to reality, which is also used by most users because it is powerful; the convention style is the direction we want to go because it is simple and elegant.

## .umi

The `.umi` temporary directory is the engine of the entire Umi project. Your entry files, routes, etc. are all here. These are generated by umi's internal plug-ins and third-party plug-ins.

You usually see the following directories under `.umi`

```bash
+ .umi
  + core     # umi core
  + pluginA  # plugin A
  + presetB  # preset B
  + umi.ts   # main umi file
```

Temporary files are a very important part of the Umi framework. The framework or plugin will generate temporary files according to your code. These dirty and messy parts that need to be placed in the project are hidden here.

You can debug the code here, but don't commit him in the `.git` repository, because it is temporary, it will be deleted and regenerated every time you start umi.
