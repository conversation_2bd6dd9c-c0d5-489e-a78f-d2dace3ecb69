# config-404

404 page is the page that the server can't provide information normally when the client is browsing the web page, or the server can't respond and doesn't know the reason.

## How to use

Execute [`@umijs/create-umi-app`](https://github.com/umijs/umi/tree/3.x/packages/create-umi-app) with [npm](https://docs.npmjs.com/cli/init) or [Yarn](https://yarnpkg.com/lang/en/docs/cli/create/) to bootstrap the example:

```bash
npx @umijs/create-umi-app --example 404 404-app
# or
yarn create @umijs/umi-app --example 404 404-app
```
