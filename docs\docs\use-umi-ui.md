---
translateHelp: true
---

# Using Umi UI


> 由于 Umi 3 使用微内核架构，将之前 Umi UI 拆分到[独立的仓库](https://github.com/umijs/umi-ui)，通过加载 [@umijs/preset-ui](https://github.com/umijs/umi-ui#-%E5%BF%AB%E9%80%9F%E5%BC%80%E5%A7%8B) 使用 Umi UI。

Umi 项目的本地研发工作台

![](https://user-images.githubusercontent.com/13595509/73431180-c77ab400-437a-11ea-9baa-ebd00109b1d0.png)

# ✨ 特性

- 项目管理。集中式管理本地项目。
- 配置管理。umi / bigfish 常用项目配置。
- 任务管理。集成启动、构建、测试、代码规范检查、重新安装依赖等常用操作。
- 资产、区块管理

# 🔨 使用

在项目中执行

```bash
$ yarn add @umijs/preset-ui -D
$ UMI_UI=1 umi dev
```

详见：[Umi UI 视频介绍](https://www.bilibili.com/video/av66178967)。


# 🤔 未来

更强大的本地研发工作台。

![](https://img.alicdn.com/tfs/TB1hKMGeND1gK0jSZFKXXcJrVXa-2000-1120.png)
