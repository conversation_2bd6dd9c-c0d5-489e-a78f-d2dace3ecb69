# plugin-initial-state

See our website [@umijs/plugin-initial-state](https://umijs.org/plugins/plugin-initial-state) for more information.

## How to use

Execute [`@umijs/create-umi-app`](https://github.com/umijs/umi/tree/3.x/packages/create-umi-app) with [npm](https://docs.npmjs.com/cli/init) or [Yarn](https://yarnpkg.com/lang/en/docs/cli/create/) to bootstrap the example:

```bash
npx @umijs/create-umi-app --example plugin-initial-state plugin-initial-state-app
# or
yarn create @umijs/umi-app --example plugin-initial-state plugin-initial-state-app
```
